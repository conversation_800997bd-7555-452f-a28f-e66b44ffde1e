{"name": "timeblock-saas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@next/env": "^15.3.3", "@prisma/client": "^6.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "14.1.0", "next-auth": "^4.24.11", "prisma": "^6.8.2", "react": "^18.3.1", "react-dom": "^18.3.1", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.11.30", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "postcss": "^8.4.35", "postcss-import": "^16.0.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}