(()=>{var $;function C(G){return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},C(G)}function A(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function Q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?A(Object(J),!0).forEach(function(X){E(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):A(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function E(G,H,J){if(H=N(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function N(G){var H=z(G,"string");return C(H)=="symbol"?H:String(H)}function z(G,H){if(C(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(C(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,JG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},D={lessThanXSeconds:{one:"\u0561\u057E\u0565\u056C\u056B \u0584\u056B\u0579 \u0584\u0561\u0576 1 \u057E\u0561\u0575\u0580\u056F\u0575\u0561\u0576",other:"\u0561\u057E\u0565\u056C\u056B \u0584\u056B\u0579 \u0584\u0561\u0576 {{count}} \u057E\u0561\u0575\u0580\u056F\u0575\u0561\u0576"},xSeconds:{one:"1 \u057E\u0561\u0575\u0580\u056F\u0575\u0561\u0576",other:"{{count}} \u057E\u0561\u0575\u0580\u056F\u0575\u0561\u0576"},halfAMinute:"\u056F\u0565\u057D \u0580\u0578\u057A\u0565",lessThanXMinutes:{one:"\u0561\u057E\u0565\u056C\u056B \u0584\u056B\u0579 \u0584\u0561\u0576 1 \u0580\u0578\u057A\u0565",other:"\u0561\u057E\u0565\u056C\u056B \u0584\u056B\u0579 \u0584\u0561\u0576 {{count}} \u0580\u0578\u057A\u0565"},xMinutes:{one:"1 \u0580\u0578\u057A\u0565",other:"{{count}} \u0580\u0578\u057A\u0565"},aboutXHours:{one:"\u0574\u0578\u057F 1 \u056A\u0561\u0574",other:"\u0574\u0578\u057F {{count}} \u056A\u0561\u0574"},xHours:{one:"1 \u056A\u0561\u0574",other:"{{count}} \u056A\u0561\u0574"},xDays:{one:"1 \u0585\u0580",other:"{{count}} \u0585\u0580"},aboutXWeeks:{one:"\u0574\u0578\u057F 1 \u0577\u0561\u0562\u0561\u0569",other:"\u0574\u0578\u057F {{count}} \u0577\u0561\u0562\u0561\u0569"},xWeeks:{one:"1 \u0577\u0561\u0562\u0561\u0569",other:"{{count}} \u0577\u0561\u0562\u0561\u0569"},aboutXMonths:{one:"\u0574\u0578\u057F 1 \u0561\u0574\u056B\u057D",other:"\u0574\u0578\u057F {{count}} \u0561\u0574\u056B\u057D"},xMonths:{one:"1 \u0561\u0574\u056B\u057D",other:"{{count}} \u0561\u0574\u056B\u057D"},aboutXYears:{one:"\u0574\u0578\u057F 1 \u057F\u0561\u0580\u056B",other:"\u0574\u0578\u057F {{count}} \u057F\u0561\u0580\u056B"},xYears:{one:"1 \u057F\u0561\u0580\u056B",other:"{{count}} \u057F\u0561\u0580\u056B"},overXYears:{one:"\u0561\u057E\u0565\u056C\u056B \u0584\u0561\u0576 1 \u057F\u0561\u0580\u056B",other:"\u0561\u057E\u0565\u056C\u056B \u0584\u0561\u0576 {{count}} \u057F\u0561\u0580\u056B"},almostXYears:{one:"\u0570\u0561\u0574\u0561\u0580\u0575\u0561 1 \u057F\u0561\u0580\u056B",other:"\u0570\u0561\u0574\u0561\u0580\u0575\u0561 {{count}} \u057F\u0561\u0580\u056B"}},S=function G(H,J,X){var Y,Z=D[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return Y+" \u0570\u0565\u057F\u0578";else return Y+" \u0561\u057C\u0561\u057B";return Y};function K(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var M={full:"d MMMM, y, EEEE",long:"d MMMM, y",medium:"d MMM, y",short:"dd.MM.yyyy"},R={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} '\u056A\u2024'{{time}}",long:"{{date}} '\u056A\u2024'{{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:K({formats:M,defaultWidth:"full"}),time:K({formats:R,defaultWidth:"full"}),dateTime:K({formats:L,defaultWidth:"full"})},j={lastWeek:"'\u0576\u0561\u056D\u0578\u0580\u0564' eeee p'\u058A\u056B\u0576'",yesterday:"'\u0565\u0580\u0565\u056F' p'\u058A\u056B\u0576'",today:"'\u0561\u0575\u057D\u0585\u0580' p'\u058A\u056B\u0576'",tomorrow:"'\u057E\u0561\u0572\u0568' p'\u058A\u056B\u0576'",nextWeek:"'\u0570\u0561\u057B\u0578\u0580\u0564' eeee p'\u058A\u056B\u0576'",other:"P"},w=function G(H,J,X,Y){return j[H]};function I(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,B=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[B]||G.formattingValues[Z]}else{var T=G.defaultWidth,q=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[q]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(H):H;return Y[U]}}var _={narrow:["\u0554","\u0544"],abbreviated:["\u0554\u0531","\u0544\u0539"],wide:["\u0554\u0580\u056B\u057D\u057F\u0578\u057D\u056B\u0581 \u0561\u057C\u0561\u057B","\u0544\u0565\u0580 \u0569\u057E\u0561\u0580\u056F\u0578\u0582\u0569\u0575\u0561\u0576"]},f={narrow:["1","2","3","4"],abbreviated:["\u05541","\u05542","\u05543","\u05544"],wide:["1\u058A\u056B\u0576 \u0584\u0561\u057C\u0578\u0580\u0564","2\u058A\u0580\u0564 \u0584\u0561\u057C\u0578\u0580\u0564","3\u058A\u0580\u0564 \u0584\u0561\u057C\u0578\u0580\u0564","4\u058A\u0580\u0564 \u0584\u0561\u057C\u0578\u0580\u0564"]},v={narrow:["\u0540","\u0553","\u0544","\u0531","\u0544","\u0540","\u0540","\u0555","\u054D","\u0540","\u0546","\u0534"],abbreviated:["\u0570\u0578\u0582\u0576","\u0583\u0565\u057F","\u0574\u0561\u0580","\u0561\u057A\u0580","\u0574\u0561\u0575","\u0570\u0578\u0582\u0576","\u0570\u0578\u0582\u056C","\u0585\u0563\u057D","\u057D\u0565\u057A","\u0570\u0578\u056F","\u0576\u0578\u0575","\u0564\u0565\u056F"],wide:["\u0570\u0578\u0582\u0576\u057E\u0561\u0580","\u0583\u0565\u057F\u0580\u057E\u0561\u0580","\u0574\u0561\u0580\u057F","\u0561\u057A\u0580\u056B\u056C","\u0574\u0561\u0575\u056B\u057D","\u0570\u0578\u0582\u0576\u056B\u057D","\u0570\u0578\u0582\u056C\u056B\u057D","\u0585\u0563\u0578\u057D\u057F\u0578\u057D","\u057D\u0565\u057A\u057F\u0565\u0574\u0562\u0565\u0580","\u0570\u0578\u056F\u057F\u0565\u0574\u0562\u0565\u0580","\u0576\u0578\u0575\u0565\u0574\u0562\u0565\u0580","\u0564\u0565\u056F\u057F\u0565\u0574\u0562\u0565\u0580"]},F={narrow:["\u053F","\u0535","\u0535","\u0549","\u0540","\u0548","\u0547"],short:["\u056F\u0580","\u0565\u0580","\u0565\u0584","\u0579\u0584","\u0570\u0563","\u0578\u0582\u0580","\u0577\u0562"],abbreviated:["\u056F\u056B\u0580","\u0565\u0580\u056F","\u0565\u0580\u0584","\u0579\u0578\u0580","\u0570\u0576\u0563","\u0578\u0582\u0580\u0562","\u0577\u0561\u0562"],wide:["\u056F\u056B\u0580\u0561\u056F\u056B","\u0565\u0580\u056F\u0578\u0582\u0577\u0561\u0562\u0569\u056B","\u0565\u0580\u0565\u0584\u0577\u0561\u0562\u0569\u056B","\u0579\u0578\u0580\u0565\u0584\u0577\u0561\u0562\u0569\u056B","\u0570\u056B\u0576\u0563\u0577\u0561\u0562\u0569\u056B","\u0578\u0582\u0580\u0562\u0561\u0569","\u0577\u0561\u0562\u0561\u0569"]},P={narrow:{am:"a",pm:"p",midnight:"\u056F\u0565\u057D\u0563\u0577",noon:"\u056F\u0565\u057D\u0585\u0580",morning:"\u0561\u057C\u0561\u057E\u0578\u057F",afternoon:"\u0581\u0565\u0580\u0565\u056F",evening:"\u0565\u0580\u0565\u056F\u0578",night:"\u0563\u056B\u0577\u0565\u0580"},abbreviated:{am:"AM",pm:"PM",midnight:"\u056F\u0565\u057D\u0563\u056B\u0577\u0565\u0580",noon:"\u056F\u0565\u057D\u0585\u0580",morning:"\u0561\u057C\u0561\u057E\u0578\u057F",afternoon:"\u0581\u0565\u0580\u0565\u056F",evening:"\u0565\u0580\u0565\u056F\u0578",night:"\u0563\u056B\u0577\u0565\u0580"},wide:{am:"a.m.",pm:"p.m.",midnight:"\u056F\u0565\u057D\u0563\u056B\u0577\u0565\u0580",noon:"\u056F\u0565\u057D\u0585\u0580",morning:"\u0561\u057C\u0561\u057E\u0578\u057F",afternoon:"\u0581\u0565\u0580\u0565\u056F",evening:"\u0565\u0580\u0565\u056F\u0578",night:"\u0563\u056B\u0577\u0565\u0580"}},k={narrow:{am:"a",pm:"p",midnight:"\u056F\u0565\u057D\u0563\u0577",noon:"\u056F\u0565\u057D\u0585\u0580",morning:"\u0561\u057C\u0561\u057E\u0578\u057F\u0568",afternoon:"\u0581\u0565\u0580\u0565\u056F\u0568",evening:"\u0565\u0580\u0565\u056F\u0578\u0575\u0561\u0576",night:"\u0563\u056B\u0577\u0565\u0580\u0568"},abbreviated:{am:"AM",pm:"PM",midnight:"\u056F\u0565\u057D\u0563\u056B\u0577\u0565\u0580\u056B\u0576",noon:"\u056F\u0565\u057D\u0585\u0580\u056B\u0576",morning:"\u0561\u057C\u0561\u057E\u0578\u057F\u0568",afternoon:"\u0581\u0565\u0580\u0565\u056F\u0568",evening:"\u0565\u0580\u0565\u056F\u0578\u0575\u0561\u0576",night:"\u0563\u056B\u0577\u0565\u0580\u0568"},wide:{am:"a.m.",pm:"p.m.",midnight:"\u056F\u0565\u057D\u0563\u056B\u0577\u0565\u0580\u056B\u0576",noon:"\u056F\u0565\u057D\u0585\u0580\u056B\u0576",morning:"\u0561\u057C\u0561\u057E\u0578\u057F\u0568",afternoon:"\u0581\u0565\u0580\u0565\u056F\u0568",evening:"\u0565\u0580\u0565\u056F\u0578\u0575\u0561\u0576",night:"\u0563\u056B\u0577\u0565\u0580\u0568"}},b=function G(H,J){var X=Number(H),Y=X%100;if(Y<10){if(Y%10===1)return X+"\u058A\u056B\u0576"}return X+"\u058A\u0580\u0564"},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:I({values:v,defaultWidth:"wide"}),day:I({values:F,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var B=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(T)?c(T,function(x){return x.test(B)}):m(T,function(x){return x.test(B)}),U;U=G.valueCallback?G.valueCallback(q):q,U=J.valueCallback?J.valueCallback(U):U;var HG=H.slice(B.length);return{value:U,rest:HG}}}function m(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function c(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function y(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var B=G.valueCallback?G.valueCallback(Z[0]):Z[0];B=J.valueCallback?J.valueCallback(B):B;var T=H.slice(Y.length);return{value:B,rest:T}}}var p=/^(\d+)((-|֊)?(ին|րդ))?/i,d=/\d+/i,g={narrow:/^(Ք|Մ)/i,abbreviated:/^(Ք\.?\s?Ա\.?|Մ\.?\s?Թ\.?\s?Ա\.?|Մ\.?\s?Թ\.?|Ք\.?\s?Հ\.?)/i,wide:/^(քրիստոսից առաջ|մեր թվարկությունից առաջ|մեր թվարկության|քրիստոսից հետո)/i},u={any:[/^ք/i,/^մ/i]},l={narrow:/^[1234]/i,abbreviated:/^ք[1234]/i,wide:/^[1234]((-|֊)?(ին|րդ)) քառորդ/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[հփմաօսնդ]/i,abbreviated:/^(հուն|փետ|մար|ապր|մայ|հուն|հուլ|օգս|սեպ|հոկ|նոյ|դեկ)/i,wide:/^(հունվար|փետրվար|մարտ|ապրիլ|մայիս|հունիս|հուլիս|օգոստոս|սեպտեմբեր|հոկտեմբեր|նոյեմբեր|դեկտեմբեր)/i},s={narrow:[/^հ/i,/^փ/i,/^մ/i,/^ա/i,/^մ/i,/^հ/i,/^հ/i,/^օ/i,/^ս/i,/^հ/i,/^ն/i,/^դ/i],any:[/^հու/i,/^փ/i,/^մար/i,/^ա/i,/^մայ/i,/^հուն/i,/^հուլ/i,/^օ/i,/^ս/i,/^հոկ/i,/^ն/i,/^դ/i]},o={narrow:/^[եչհոշկ]/i,short:/^(կր|եր|եք|չք|հգ|ուր|շբ)/i,abbreviated:/^(կիր|երկ|երք|չոր|հնգ|ուրբ|շաբ)/i,wide:/^(կիրակի|երկուշաբթի|երեքշաբթի|չորեքշաբթի|հինգշաբթի|ուրբաթ|շաբաթ)/i},r={narrow:[/^կ/i,/^ե/i,/^ե/i,/^չ/i,/^հ/i,/^(ո|Ո)/,/^շ/i],short:[/^կ/i,/^եր/i,/^եք/i,/^չ/i,/^հ/i,/^(ո|Ո)/,/^շ/i],abbreviated:[/^կ/i,/^երկ/i,/^երք/i,/^չ/i,/^հ/i,/^(ո|Ո)/,/^շ/i],wide:[/^կ/i,/^երկ/i,/^երե/i,/^չ/i,/^հ/i,/^(ո|Ո)/,/^շ/i]},a={narrow:/^([ap]|կեսգշ|կեսօր|(առավոտը?|ցերեկը?|երեկո(յան)?|գիշերը?))/i,any:/^([ap]\.?\s?m\.?|կեսգիշեր(ին)?|կեսօր(ին)?|(առավոտը?|ցերեկը?|երեկո(յան)?|գիշերը?))/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/կեսգիշեր/i,noon:/կեսօր/i,morning:/առավոտ/i,afternoon:/ցերեկ/i,evening:/երեկո/i,night:/գիշեր/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function G(H){return parseInt(H,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"wide"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},GG={code:"hy",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{hy:GG})})})();

//# debugId=54160C8EE1DF1C8B64756E2164756E21
