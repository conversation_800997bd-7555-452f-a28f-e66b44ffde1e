"use client"

import { useState } from "react"
import { format } from "date-fns"
import { useSession } from "next-auth/react"
import { useAppStore, TimeBlock } from "@/lib/store"
import { Button } from "@/components/ui/button"

interface TimeBlockFormProps {
  timeBlock?: TimeBlock
  selectedTimeSlot?: { date: Date; hour: number } | null
  onClose: () => void
  onSubmit: () => void
}

export function TimeBlockForm({ timeBlock, selectedTimeSlot, onClose, onSubmit }: TimeBlockFormProps) {
  const { data: session } = useSession()
  const { addTimeBlock, updateTimeBlock, tasks } = useAppStore()
  
  const getInitialStartTime = () => {
    if (timeBlock) {
      return format(new Date(timeBlock.startTime), "yyyy-MM-dd'T'HH:mm")
    }
    if (selectedTimeSlot) {
      const date = new Date(selectedTimeSlot.date)
      date.setHours(selectedTimeSlot.hour, 0, 0, 0)
      return format(date, "yyyy-MM-dd'T'HH:mm")
    }
    return format(new Date(), "yyyy-MM-dd'T'HH:mm")
  }

  const getInitialEndTime = () => {
    if (timeBlock) {
      return format(new Date(timeBlock.endTime), "yyyy-MM-dd'T'HH:mm")
    }
    if (selectedTimeSlot) {
      const date = new Date(selectedTimeSlot.date)
      date.setHours(selectedTimeSlot.hour + 1, 0, 0, 0)
      return format(date, "yyyy-MM-dd'T'HH:mm")
    }
    const endTime = new Date()
    endTime.setHours(endTime.getHours() + 1)
    return format(endTime, "yyyy-MM-dd'T'HH:mm")
  }

  const [formData, setFormData] = useState({
    title: timeBlock?.title || "",
    startTime: getInitialStartTime(),
    endTime: getInitialEndTime(),
    taskId: timeBlock?.taskId || "",
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) return

    const startTime = new Date(formData.startTime)
    const endTime = new Date(formData.endTime)

    if (endTime <= startTime) {
      alert("End time must be after start time")
      return
    }

    if (timeBlock) {
      // Update existing time block
      updateTimeBlock(timeBlock.id, {
        ...formData,
        startTime,
        endTime,
        taskId: formData.taskId || undefined,
        updatedAt: new Date(),
      })
    } else {
      // Create new time block
      const newTimeBlock: TimeBlock = {
        id: crypto.randomUUID(),
        title: formData.title,
        startTime,
        endTime,
        userId: session?.user?.id || "",
        taskId: formData.taskId || undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      addTimeBlock(newTimeBlock)
    }

    onSubmit()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            {timeBlock ? "Edit Time Block" : "Create Time Block"}
          </h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter time block title"
              required
            />
          </div>

          {/* Start Time */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Time *
            </label>
            <input
              type="datetime-local"
              value={formData.startTime}
              onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          {/* End Time */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Time *
            </label>
            <input
              type="datetime-local"
              value={formData.endTime}
              onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          {/* Associated Task */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Associated Task (Optional)
            </label>
            <select
              value={formData.taskId}
              onChange={(e) => setFormData({ ...formData, taskId: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">No associated task</option>
              {tasks.map((task) => (
                <option key={task.id} value={task.id}>
                  {task.title}
                </option>
              ))}
            </select>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button type="submit" className="flex-1">
              {timeBlock ? "Update Time Block" : "Create Time Block"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
